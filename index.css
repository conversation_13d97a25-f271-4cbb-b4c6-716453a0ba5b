/* ProofStack - Legal Technology Platform */
/* Base styles and CSS custom properties */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
  /* Ignition UI Color Palette */
  --brand-primary: #f59e0b;
  --brand-secondary: #fbbf24;

  /* Background Colors */
  --bg-main: #0d1117;
  --bg-card: #161b22;
  --bg-border: #21262d;
  --bg-interactive: #30363d;

  /* Text Colors */
  --text-default: #e5e7eb;
  --text-muted: #4b5563;

  /* Status Colors */
  --success-bg: rgba(34, 197, 94, 0.5);
  --success-text: #86efac;
  --success-border: #15803d;

  --info-bg: rgba(59, 130, 246, 0.5);
  --info-text: #93c5fd;
  --info-border: #1d4ed8;

  --warning-bg: rgba(234, 179, 8, 0.5);
  --warning-text: #fde047;
  --warning-border: #a16207;

  --danger-bg: rgba(239, 68, 68, 0.5);
  --danger-text: #fca5a5;
  --danger-border: #dc2626;

  --special-bg: rgba(147, 51, 234, 0.5);
  --special-text: #c4b5fd;
  --special-border: #7c3aed;

  --code-text: #67e8f9;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
}

body {
  font-family: var(--font-family);
  background: var(--bg-main);
  color: var(--text-default);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ignition UI Utility Classes */
.brand-primary { color: var(--brand-primary); }
.bg-brand-primary { background-color: var(--brand-primary); }
.border-brand-primary { border-color: var(--brand-primary); }
.bg-brand-primary\/10 { background-color: rgba(245, 158, 11, 0.1); }
.border-brand-primary\/30 { border-color: rgba(245, 158, 11, 0.3); }

.brand-secondary { color: var(--brand-secondary); }
.bg-brand-secondary { background-color: var(--brand-secondary); }

/* Status Colors */
.status-success { background-color: var(--success-bg); color: var(--success-text); border-color: var(--success-border); }
.status-info { background-color: var(--info-bg); color: var(--info-text); border-color: var(--info-border); }
.status-warning { background-color: var(--warning-bg); color: var(--warning-text); border-color: var(--warning-border); }
.status-danger { background-color: var(--danger-bg); color: var(--danger-text); border-color: var(--danger-border); }
.status-special { background-color: var(--special-bg); color: var(--special-text); border-color: var(--special-border); }

/* Component Styles */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--bg-border);
  border-radius: 0.5rem;
  transition: border-color 0.2s;
}

.card:hover {
  border-color: var(--brand-primary);
}

.btn-primary {
  background-color: var(--brand-primary);
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--brand-secondary);
}

.btn-ai {
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: var(--brand-primary);
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.input-field {
  background-color: var(--bg-border);
  border: 1px solid var(--bg-interactive);
  border-radius: 0.5rem;
  color: var(--text-default);
}

.input-field:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--brand-primary);
}

/* Hover animations */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #34d399 0%, #60a5fa 50%, #8b5cf6 100%);
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: var(--progress-width, 100%);
  }
}

/* Utility animation classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.1), transparent);
  background-size: 200px 100%;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-progress {
  animation: progress 1s ease-out forwards;
}

/* Staggered animations */
.animate-stagger-1 { animation-delay: 0.1s; }
.animate-stagger-2 { animation-delay: 0.2s; }
.animate-stagger-3 { animation-delay: 0.3s; }
.animate-stagger-4 { animation-delay: 0.4s; }
.animate-stagger-5 { animation-delay: 0.5s; }

/* Hover animations */
.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, var(--bg-border) 25%, var(--bg-interactive) 50%, var(--bg-border) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  :root {
    --font-size-5xl: 2.5rem;
    --font-size-4xl: 2rem;
    --font-size-3xl: 1.5rem;
  }
}

/* Print styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .no-print {
    display: none;
  }
}
