// Legal Standards Framework for ProofStack
// Federal Rules of Evidence (FRE) and Indiana Rules of Evidence (IRE)

export interface LegalRule {
  id: string;
  title: string;
  jurisdiction: 'Federal' | 'Indiana' | 'Both';
  ruleNumber: string;
  category: 'Authentication' | 'BestEvidence' | 'Hearsay' | 'Relevance' | 'Privilege' | 'Discovery';
  description: string;
  requirements: string[];
  exceptions?: string[];
  relatedRules?: string[];
  citation: string;
}

export interface AnalysisCriteria {
  ruleId: string;
  weight: number; // 1-10, importance in overall analysis
  requiredElements: string[];
  scoringFactors: ScoringFactor[];
}

export interface ScoringFactor {
  factor: string;
  description: string;
  maxPoints: number;
  evaluationCriteria: string[];
}

export interface ComplianceResult {
  ruleId: string;
  compliant: boolean;
  score: number;
  maxScore: number;
  findings: Finding[];
  recommendations: string[];
}

export interface Finding {
  type: 'strength' | 'weakness' | 'missing' | 'concern';
  description: string;
  impact: 'high' | 'medium' | 'low';
  ruleReference: string;
}

// Federal Rules of Evidence
export const FEDERAL_RULES: LegalRule[] = [
  {
    id: 'fre-901',
    title: 'Authenticating or Identifying Evidence',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 901',
    category: 'Authentication',
    description: 'To satisfy the requirement of authenticating or identifying an item of evidence, the proponent must produce evidence sufficient to support a finding that the item is what the proponent claims it is.',
    requirements: [
      'Evidence sufficient to support a finding of authenticity',
      'Testimony of witness with knowledge',
      'Nonexpert opinion about handwriting',
      'Comparison with authenticated specimens',
      'Distinctive characteristics and the like',
      'Evidence about a process or system',
      'Evidence about public records',
      'Evidence about ancient documents or data compilations',
      'Evidence about a process or system used to produce a result',
      'Methods provided by a statute or rule'
    ],
    citation: 'Fed. R. Evid. 901',
    relatedRules: ['fre-902', 'fre-1001']
  },
  {
    id: 'fre-902',
    title: 'Evidence That Is Self-Authenticating',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 902',
    category: 'Authentication',
    description: 'The following items of evidence are self-authenticating; they require no extrinsic evidence of authenticity in order to be admitted.',
    requirements: [
      'Domestic public documents that are sealed and signed',
      'Domestic public documents that are not sealed but are signed and certified',
      'Foreign public documents',
      'Certified copies of public records',
      'Official publications',
      'Newspapers and periodicals',
      'Trade inscriptions and the like',
      'Acknowledged documents',
      'Commercial paper and related documents',
      'Presumptions under a federal statute',
      'Certified domestic records of a regularly conducted activity',
      'Certified foreign records of a regularly conducted activity',
      'Machine-generated digital evidence'
    ],
    citation: 'Fed. R. Evid. 902'
  },
  {
    id: 'fre-1001',
    title: 'Definitions (Best Evidence Rule)',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 1001-1008',
    category: 'BestEvidence',
    description: 'An original of a writing or recording means the writing or recording itself or any counterpart intended to have the same effect by the person who executed or issued it.',
    requirements: [
      'Original document required to prove content',
      'Duplicate admissible to same extent as original unless genuine question about authenticity',
      'Original not required if lost or destroyed in good faith',
      'Original not required if not obtainable by judicial process',
      'Original not required if in possession of opponent who fails to produce after notice'
    ],
    citation: 'Fed. R. Evid. 1001-1008',
    relatedRules: ['fre-901', 'fre-902']
  },
  {
    id: 'fre-401',
    title: 'Test for Relevant Evidence',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 401',
    category: 'Relevance',
    description: 'Evidence is relevant if it has any tendency to make a fact more or less probable than it would be without the evidence, and the fact is of consequence in determining the action.',
    requirements: [
      'Tendency to make fact more or less probable',
      'Fact must be of consequence in determining the action'
    ],
    citation: 'Fed. R. Evid. 401',
    relatedRules: ['fre-402', 'fre-403']
  },
  {
    id: 'fre-403',
    title: 'Excluding Relevant Evidence for Prejudice, Confusion, or Other Reasons',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 403',
    category: 'Relevance',
    description: 'The court may exclude relevant evidence if its probative value is substantially outweighed by a danger of unfair prejudice, confusing the issues, misleading the jury, undue delay, wasting time, or needlessly presenting cumulative evidence.',
    requirements: [
      'Evidence must be relevant under FRE 401',
      'Probative value not substantially outweighed by prejudicial effect',
      'No undue confusion or misleading of jury',
      'No undue delay or waste of time'
    ],
    citation: 'Fed. R. Evid. 403',
    relatedRules: ['fre-401', 'fre-402']
  },
  {
    id: 'fre-803',
    title: 'Exceptions to the Rule Against Hearsay',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 803',
    category: 'Hearsay',
    description: 'The following are not excluded by the rule against hearsay, regardless of whether the declarant is available as a witness.',
    requirements: [
      'Statement falls within recognized exception',
      'Reliability indicators present',
      'Necessity for admission established'
    ],
    exceptions: [
      'Present sense impression',
      'Excited utterance',
      'Then-existing mental, emotional, or physical condition',
      'Statement made for medical diagnosis or treatment',
      'Recorded recollection',
      'Records of a regularly conducted activity',
      'Absence of a record of a regularly conducted activity',
      'Public records',
      'Public records of vital statistics',
      'Absence of a public record',
      'Records of religious organizations concerning personal or family history',
      'Certificates of marriage, baptism, and similar ceremonies',
      'Family records',
      'Records of documents that affect an interest in property',
      'Statements in documents that affect an interest in property',
      'Statements in ancient documents',
      'Market reports and similar commercial publications',
      'Learned treatises',
      'Reputation concerning personal or family history',
      'Reputation concerning boundaries or general history',
      'Reputation concerning character',
      'Judgment of a previous conviction',
      'Judgments involving personal, family, or general history'
    ],
    citation: 'Fed. R. Evid. 803'
  }
];

// Indiana Rules of Evidence (mirror Federal with state-specific variations)
export const INDIANA_RULES: LegalRule[] = [
  {
    id: 'ire-901',
    title: 'Authentication and Identification',
    jurisdiction: 'Indiana',
    ruleNumber: 'IRE 901',
    category: 'Authentication',
    description: 'The requirement of authentication or identification as a condition precedent to admissibility is satisfied by evidence sufficient to support a finding that the matter in question is what its proponent claims.',
    requirements: [
      'Evidence sufficient to support finding of authenticity',
      'Testimony of witness with knowledge',
      'Nonexpert opinion on handwriting',
      'Comparison by trier or expert witness',
      'Distinctive characteristics and circumstances',
      'Voice identification',
      'Telephone conversations',
      'Public records or reports',
      'Ancient documents or data compilation',
      'Process or system',
      'Methods provided by statute or rule'
    ],
    citation: 'Ind. R. Evid. 901'
  }
];

// Analysis Criteria for each rule
export const ANALYSIS_CRITERIA: AnalysisCriteria[] = [
  {
    ruleId: 'fre-901',
    weight: 9,
    requiredElements: [
      'Chain of custody documentation',
      'Witness testimony or affidavit',
      'Technical documentation of collection process',
      'Metadata preservation evidence'
    ],
    scoringFactors: [
      {
        factor: 'Chain of Custody',
        description: 'Complete documentation of evidence handling from collection to presentation',
        maxPoints: 25,
        evaluationCriteria: [
          'Unbroken chain documented',
          'All handlers identified',
          'Transfer procedures followed',
          'Storage conditions documented',
          'Access controls maintained'
        ]
      },
      {
        factor: 'Technical Authentication',
        description: 'Technical methods used to verify evidence integrity',
        maxPoints: 20,
        evaluationCriteria: [
          'Hash values calculated and verified',
          'Digital signatures present',
          'Timestamps verified',
          'Metadata preserved',
          'Collection tools documented'
        ]
      },
      {
        factor: 'Witness Testimony',
        description: 'Competent witness testimony supporting authenticity',
        maxPoints: 15,
        evaluationCriteria: [
          'Witness has personal knowledge',
          'Witness is competent',
          'Testimony is detailed and specific',
          'Witness available for cross-examination'
        ]
      }
    ]
  }
];

export class LegalStandardsEngine {
  private rules: Map<string, LegalRule> = new Map();
  private criteria: Map<string, AnalysisCriteria> = new Map();

  constructor() {
    // Load all rules
    [...FEDERAL_RULES, ...INDIANA_RULES].forEach(rule => {
      this.rules.set(rule.id, rule);
    });

    // Load analysis criteria
    ANALYSIS_CRITERIA.forEach(criteria => {
      this.criteria.set(criteria.ruleId, criteria);
    });
  }

  getRuleById(ruleId: string): LegalRule | undefined {
    return this.rules.get(ruleId);
  }

  getRulesByCategory(category: LegalRule['category']): LegalRule[] {
    return Array.from(this.rules.values()).filter(rule => rule.category === category);
  }

  getCriteriaForRule(ruleId: string): AnalysisCriteria | undefined {
    return this.criteria.get(ruleId);
  }

  analyzeCompliance(ruleId: string, evidence: any): ComplianceResult {
    const rule = this.getRuleById(ruleId);
    const criteria = this.getCriteriaForRule(ruleId);

    if (!rule || !criteria) {
      throw new Error(`Rule or criteria not found for ${ruleId}`);
    }

    // This would contain the actual analysis logic
    // For now, returning a template structure
    return {
      ruleId,
      compliant: false,
      score: 0,
      maxScore: criteria.scoringFactors.reduce((sum, factor) => sum + factor.maxPoints, 0),
      findings: [],
      recommendations: []
    };
  }

  generateCitations(ruleIds: string[]): string[] {
    return ruleIds
      .map(id => this.getRuleById(id))
      .filter(rule => rule !== undefined)
      .map(rule => rule!.citation);
  }
}
