// Legal Standards Framework for ProofStack
// Federal Rules of Evidence (FRE) and Indiana Rules of Evidence (IRE)

export interface LegalRule {
  id: string;
  title: string;
  jurisdiction: 'Federal' | 'Indiana' | 'Both';
  ruleNumber: string;
  category:
    | 'Authentication'
    | 'BestEvidence'
    | 'Hearsay'
    | 'Relevance'
    | 'Privilege'
    | 'Discovery';
  description: string;
  requirements: string[];
  exceptions?: string[];
  relatedRules?: string[];
  citation: string;
}

export interface AnalysisCriteria {
  ruleId: string;
  weight: number; // 1-10, importance in overall analysis
  requiredElements: string[];
  scoringFactors: ScoringFactor[];
}

export interface ScoringFactor {
  factor: string;
  description: string;
  maxPoints: number;
  evaluationCriteria: string[];
}

export interface ComplianceResult {
  ruleId: string;
  compliant: boolean;
  score: number;
  maxScore: number;
  findings: Finding[];
  recommendations: string[];
}

export interface Finding {
  type: 'strength' | 'weakness' | 'missing' | 'concern';
  description: string;
  impact: 'high' | 'medium' | 'low';
  ruleReference: string;
}

// Federal Rules of Evidence
export const FEDERAL_RULES: LegalRule[] = [
  {
    id: 'fre-901',
    title: 'Authenticating or Identifying Evidence',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 901',
    category: 'Authentication',
    description:
      'To satisfy the requirement of authenticating or identifying an item of evidence, the proponent must produce evidence sufficient to support a finding that the item is what the proponent claims it is.',
    requirements: [
      'Evidence sufficient to support a finding of authenticity',
      'Testimony of witness with knowledge',
      'Nonexpert opinion about handwriting',
      'Comparison with authenticated specimens',
      'Distinctive characteristics and the like',
      'Evidence about a process or system',
      'Evidence about public records',
      'Evidence about ancient documents or data compilations',
      'Evidence about a process or system used to produce a result',
      'Methods provided by a statute or rule',
    ],
    citation: 'Fed. R. Evid. 901',
    relatedRules: ['fre-902', 'fre-1001'],
  },
  {
    id: 'fre-902',
    title: 'Evidence That Is Self-Authenticating',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 902',
    category: 'Authentication',
    description:
      'The following items of evidence are self-authenticating; they require no extrinsic evidence of authenticity in order to be admitted.',
    requirements: [
      'Domestic public documents that are sealed and signed',
      'Domestic public documents that are not sealed but are signed and certified',
      'Foreign public documents',
      'Certified copies of public records',
      'Official publications',
      'Newspapers and periodicals',
      'Trade inscriptions and the like',
      'Acknowledged documents',
      'Commercial paper and related documents',
      'Presumptions under a federal statute',
      'Certified domestic records of a regularly conducted activity',
      'Certified foreign records of a regularly conducted activity',
      'Machine-generated digital evidence',
    ],
    citation: 'Fed. R. Evid. 902',
  },
  {
    id: 'fre-1001',
    title: 'Definitions (Best Evidence Rule)',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 1001-1008',
    category: 'BestEvidence',
    description:
      'An original of a writing or recording means the writing or recording itself or any counterpart intended to have the same effect by the person who executed or issued it.',
    requirements: [
      'Original document required to prove content',
      'Duplicate admissible to same extent as original unless genuine question about authenticity',
      'Original not required if lost or destroyed in good faith',
      'Original not required if not obtainable by judicial process',
      'Original not required if in possession of opponent who fails to produce after notice',
    ],
    citation: 'Fed. R. Evid. 1001-1008',
    relatedRules: ['fre-901', 'fre-902'],
  },
  {
    id: 'fre-401',
    title: 'Test for Relevant Evidence',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 401',
    category: 'Relevance',
    description:
      'Evidence is relevant if it has any tendency to make a fact more or less probable than it would be without the evidence, and the fact is of consequence in determining the action.',
    requirements: [
      'Tendency to make fact more or less probable',
      'Fact must be of consequence in determining the action',
    ],
    citation: 'Fed. R. Evid. 401',
    relatedRules: ['fre-402', 'fre-403'],
  },
  {
    id: 'fre-403',
    title:
      'Excluding Relevant Evidence for Prejudice, Confusion, or Other Reasons',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 403',
    category: 'Relevance',
    description:
      'The court may exclude relevant evidence if its probative value is substantially outweighed by a danger of unfair prejudice, confusing the issues, misleading the jury, undue delay, wasting time, or needlessly presenting cumulative evidence.',
    requirements: [
      'Evidence must be relevant under FRE 401',
      'Probative value not substantially outweighed by prejudicial effect',
      'No undue confusion or misleading of jury',
      'No undue delay or waste of time',
    ],
    citation: 'Fed. R. Evid. 403',
    relatedRules: ['fre-401', 'fre-402'],
  },
  {
    id: 'fre-803',
    title: 'Exceptions to the Rule Against Hearsay',
    jurisdiction: 'Federal',
    ruleNumber: 'FRE 803',
    category: 'Hearsay',
    description:
      'The following are not excluded by the rule against hearsay, regardless of whether the declarant is available as a witness.',
    requirements: [
      'Statement falls within recognized exception',
      'Reliability indicators present',
      'Necessity for admission established',
    ],
    exceptions: [
      'Present sense impression',
      'Excited utterance',
      'Then-existing mental, emotional, or physical condition',
      'Statement made for medical diagnosis or treatment',
      'Recorded recollection',
      'Records of a regularly conducted activity',
      'Absence of a record of a regularly conducted activity',
      'Public records',
      'Public records of vital statistics',
      'Absence of a public record',
      'Records of religious organizations concerning personal or family history',
      'Certificates of marriage, baptism, and similar ceremonies',
      'Family records',
      'Records of documents that affect an interest in property',
      'Statements in documents that affect an interest in property',
      'Statements in ancient documents',
      'Market reports and similar commercial publications',
      'Learned treatises',
      'Reputation concerning personal or family history',
      'Reputation concerning boundaries or general history',
      'Reputation concerning character',
      'Judgment of a previous conviction',
      'Judgments involving personal, family, or general history',
    ],
    citation: 'Fed. R. Evid. 803',
  },
];

// Indiana Rules of Evidence (mirror Federal with state-specific variations)
export const INDIANA_RULES: LegalRule[] = [
  {
    id: 'ire-901',
    title: 'Authentication and Identification',
    jurisdiction: 'Indiana',
    ruleNumber: 'IRE 901',
    category: 'Authentication',
    description:
      'The requirement of authentication or identification as a condition precedent to admissibility is satisfied by evidence sufficient to support a finding that the matter in question is what its proponent claims.',
    requirements: [
      'Evidence sufficient to support finding of authenticity',
      'Testimony of witness with knowledge',
      'Nonexpert opinion on handwriting',
      'Comparison by trier or expert witness',
      'Distinctive characteristics and circumstances',
      'Voice identification',
      'Telephone conversations',
      'Public records or reports',
      'Ancient documents or data compilation',
      'Process or system',
      'Methods provided by statute or rule',
    ],
    citation: 'Ind. R. Evid. 901',
  },
];

// Analysis Criteria for each rule
export const ANALYSIS_CRITERIA: AnalysisCriteria[] = [
  {
    ruleId: 'fre-901',
    weight: 9,
    requiredElements: [
      'Chain of custody documentation',
      'Witness testimony or affidavit',
      'Technical documentation of collection process',
      'Metadata preservation evidence',
    ],
    scoringFactors: [
      {
        factor: 'Chain of Custody',
        description:
          'Complete documentation of evidence handling from collection to presentation',
        maxPoints: 25,
        evaluationCriteria: [
          'Unbroken chain documented',
          'All handlers identified',
          'Transfer procedures followed',
          'Storage conditions documented',
          'Access controls maintained',
        ],
      },
      {
        factor: 'Technical Authentication',
        description: 'Technical methods used to verify evidence integrity',
        maxPoints: 20,
        evaluationCriteria: [
          'Hash values calculated and verified',
          'Digital signatures present',
          'Timestamps verified',
          'Metadata preserved',
          'Collection tools documented',
        ],
      },
      {
        factor: 'Witness Testimony',
        description: 'Competent witness testimony supporting authenticity',
        maxPoints: 15,
        evaluationCriteria: [
          'Witness has personal knowledge',
          'Witness is competent',
          'Testimony is detailed and specific',
          'Witness available for cross-examination',
        ],
      },
    ],
  },
  {
    ruleId: 'fre-902',
    weight: 8,
    requiredElements: [
      'Document type classification',
      'Authority verification',
    ],
    scoringFactors: [
      {
        factor: 'Self-Authentication',
        description: 'Evidence qualifies for self-authentication under FRE 902',
        maxPoints: 30,
        evaluationCriteria: [
          'Public record status verified',
          'Issuing authority identified',
          'Official seal or certification present',
          'Proper format and appearance',
        ],
      },
    ],
  },
  {
    ruleId: 'fre-1001',
    weight: 7,
    requiredElements: [
      'Original vs copy determination',
      'Justification for copies',
    ],
    scoringFactors: [
      {
        factor: 'Best Evidence',
        description: 'Original document or acceptable copy provided',
        maxPoints: 25,
        evaluationCriteria: [
          'Original document provided',
          'Copy justified by circumstances',
          'Original unavailability explained',
          'Copy accuracy verified',
        ],
      },
    ],
  },
  {
    ruleId: 'fre-803',
    weight: 8,
    requiredElements: ['Hearsay analysis', 'Exception qualification'],
    scoringFactors: [
      {
        factor: 'Hearsay Exception',
        description: 'Evidence qualifies for hearsay exception',
        maxPoints: 30,
        evaluationCriteria: [
          'Business record requirements met',
          'Regular course of business established',
          'Record keeper identified',
          'Contemporaneous creation verified',
        ],
      },
    ],
  },
  {
    ruleId: 'fre-401',
    weight: 9,
    requiredElements: ['Relevance determination', 'Probative value assessment'],
    scoringFactors: [
      {
        factor: 'Relevance',
        description: 'Evidence is relevant to case issues',
        maxPoints: 25,
        evaluationCriteria: [
          'Logical connection to case facts',
          'Tendency to prove material fact',
          'Probative value identified',
          'Clear relevance articulated',
        ],
      },
    ],
  },
  {
    ruleId: 'fre-403',
    weight: 8,
    requiredElements: ['Prejudice assessment', 'Probative value balancing'],
    scoringFactors: [
      {
        factor: 'Prejudice Balance',
        description: 'Probative value outweighs prejudicial effect',
        maxPoints: 25,
        evaluationCriteria: [
          'Prejudicial risk assessed',
          'Probative value quantified',
          'Alternative evidence considered',
          'Limiting instructions available',
        ],
      },
    ],
  },
];

export class LegalStandardsEngine {
  private rules: Map<string, LegalRule> = new Map();
  private criteria: Map<string, AnalysisCriteria> = new Map();

  constructor() {
    // Load all rules
    [...FEDERAL_RULES, ...INDIANA_RULES].forEach((rule) => {
      this.rules.set(rule.id, rule);
    });

    // Load analysis criteria
    ANALYSIS_CRITERIA.forEach((criteria) => {
      this.criteria.set(criteria.ruleId, criteria);
    });
  }

  getRuleById(ruleId: string): LegalRule | undefined {
    return this.rules.get(ruleId);
  }

  getRulesByCategory(category: LegalRule['category']): LegalRule[] {
    return Array.from(this.rules.values()).filter(
      (rule) => rule.category === category
    );
  }

  getCriteriaForRule(ruleId: string): AnalysisCriteria | undefined {
    return this.criteria.get(ruleId);
  }

  analyzeCompliance(ruleId: string, evidence: any): ComplianceResult {
    const rule = this.getRuleById(ruleId);
    const criteria = this.getCriteriaForRule(ruleId);

    if (!rule || !criteria) {
      throw new Error(`Rule or criteria not found for ${ruleId}`);
    }

    // This would contain the actual analysis logic
    // For now, returning a template structure
    return {
      ruleId,
      compliant: false,
      score: 0,
      maxScore: criteria.scoringFactors.reduce(
        (sum, factor) => sum + factor.maxPoints,
        0
      ),
      findings: [],
      recommendations: [],
    };
  }

  generateCitations(ruleIds: string[]): string[] {
    return ruleIds
      .map((id) => this.getRuleById(id))
      .filter((rule) => rule !== undefined)
      .map((rule) => rule!.citation);
  }

  evaluateRule(ruleId: string, evidence: any): ComplianceResult {
    // Normalize rule ID to match the format used in the rules array
    const normalizedRuleId = this.normalizeRuleId(ruleId);
    const rule = this.getRuleById(normalizedRuleId);
    const criteria = this.getCriteriaForRule(normalizedRuleId);

    if (!rule || !criteria) {
      throw new Error(`Unknown rule: ${ruleId}`);
    }

    // Basic scoring logic based on evidence metadata
    let score = 0;
    const findings: string[] = [];
    const recommendations: string[] = [];
    const maxScore = criteria.scoringFactors.reduce(
      (sum, factor) => sum + factor.maxPoints,
      0
    );

    // Rule-specific evaluation logic
    switch (normalizedRuleId) {
      case 'fre-901':
        score = this.evaluateFRE901(evidence, findings, recommendations);
        break;
      case 'fre-902':
        score = this.evaluateFRE902(evidence, findings, recommendations);
        break;
      case 'fre-1001':
        score = this.evaluateFRE1002(evidence, findings, recommendations);
        break;
      case 'fre-803':
        score = this.evaluateFRE803_6(evidence, findings, recommendations);
        break;
      case 'fre-401':
        score = this.evaluateFRE401(evidence, findings, recommendations);
        break;
      case 'fre-403':
        score = this.evaluateFRE403(evidence, findings, recommendations);
        break;
      default:
        score = 50; // Default neutral score
        findings.push('Rule evaluation not fully implemented');
    }

    return {
      ruleId,
      compliant: score >= 70,
      score: Math.min(score, maxScore),
      maxScore,
      findings,
      recommendations,
    };
  }

  analyzeEvidence(evidence: any): ComplianceResult[] {
    const applicableRules = [
      'fre-901',
      'fre-902',
      'fre-1001',
      'fre-803',
      'fre-401',
      'fre-403',
    ];
    return applicableRules.map((ruleId) => this.evaluateRule(ruleId, evidence));
  }

  private normalizeRuleId(ruleId: string): string {
    // Convert test format (FRE_901) to actual format (fre-901)
    return ruleId
      .toLowerCase()
      .replace(/_/g, '-')
      .replace('fre-1002', 'fre-1001')
      .replace('fre-803-6', 'fre-803');
  }

  calculateOverallCompliance(evidence: any): number {
    const results = this.analyzeEvidence(evidence);
    const totalScore = results.reduce((sum, result) => sum + result.score, 0);
    const maxPossibleScore = results.reduce(
      (sum, result) => sum + result.maxScore,
      0
    );
    return maxPossibleScore > 0
      ? Math.round((totalScore / maxPossibleScore) * 100)
      : 0;
  }

  private evaluateFRE901(
    evidence: any,
    findings: string[],
    recommendations: string[]
  ): number {
    let score = 50; // Base score

    if (evidence.metadata?.digitalSignature) {
      score += 20;
      findings.push('Digital signature present for authentication');
    } else {
      findings.push('Missing digital authentication');
      recommendations.push(
        'Obtain digital signature or other authentication method'
      );
    }

    if (evidence.metadata?.chainOfCustody?.length > 0) {
      score += 15;
      findings.push('Chain of custody documented');
    } else {
      findings.push('Chain of custody missing or incomplete');
      recommendations.push('Document complete chain of custody');
    }

    return score;
  }

  private evaluateFRE902(
    evidence: any,
    findings: string[],
    recommendations: string[]
  ): number {
    let score = 40; // Base score

    if (evidence.metadata?.documentType === 'public_record') {
      score += 40;
      findings.push('Document qualifies as self-authenticating public record');
    } else {
      findings.push('Document requires additional authentication');
      recommendations.push('Provide authentication witness or certification');
    }

    return score;
  }

  private evaluateFRE1002(
    evidence: any,
    findings: string[],
    recommendations: string[]
  ): number {
    let score = 30; // Base score

    if (evidence.metadata?.isOriginal) {
      score += 55;
      findings.push('Original document provided');
    } else if (evidence.metadata?.copyJustification) {
      score += 40;
      findings.push('Copy provided with proper justification');
    } else {
      findings.push('Copy provided without justification for original absence');
      recommendations.push(
        'Provide justification for using copy instead of original'
      );
    }

    return score;
  }

  private evaluateFRE803_6(
    evidence: any,
    findings: string[],
    recommendations: string[]
  ): number {
    let score = 30; // Base score

    if (evidence.metadata?.regularCourse && evidence.metadata?.recordKeeper) {
      score += 45;
      findings.push('Qualifies as business record exception to hearsay');
    } else if (
      evidence.metadata?.isHearsay &&
      !evidence.metadata?.hearsayException
    ) {
      score = 20;
      findings.push('Hearsay evidence without applicable exception');
      recommendations.push(
        'Establish hearsay exception or find non-hearsay alternative'
      );
    }

    return score;
  }

  private evaluateFRE401(
    evidence: any,
    findings: string[],
    recommendations: string[]
  ): number {
    let score = 40; // Base score

    const relevanceScore = evidence.metadata?.relevanceScore || 50;
    score += Math.round(relevanceScore * 0.6);

    if (relevanceScore >= 80) {
      findings.push('Evidence highly relevant to case');
    } else if (relevanceScore >= 60) {
      findings.push('Evidence moderately relevant');
    } else {
      findings.push('Evidence relevance questionable');
      recommendations.push(
        'Strengthen connection between evidence and case issues'
      );
    }

    return score;
  }

  private evaluateFRE403(
    evidence: any,
    findings: string[],
    recommendations: string[]
  ): number {
    let score = 70; // Base score (assume admissible unless prejudicial)

    if (evidence.metadata?.prejudicialRisk === 'high') {
      score -= 30;
      findings.push('High risk of unfair prejudice');
      recommendations.push(
        'Consider limiting instruction or alternative evidence'
      );
    } else if (evidence.metadata?.prejudicialRisk === 'medium') {
      score -= 15;
      findings.push('Moderate prejudicial risk');
    }

    return score;
  }
}
