{"name": "digital-evidence-assessor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:integration": "vitest run src/test/integration.test.tsx", "test:performance": "vitest run src/test/performance.test.ts", "test:unit": "vitest run --exclude src/test/integration.test.tsx src/test/performance.test.ts"}, "dependencies": {"@google/genai": "^1.7.0", "eslint": "^9.30.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.14.0", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^3.2.4"}}